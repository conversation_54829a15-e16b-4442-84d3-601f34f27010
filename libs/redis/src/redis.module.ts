/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DynamicModule, Global, Module } from '@nestjs/common';
import { Redis, RedisOptions } from 'ioredis';

const REDIS_CLIENT = Symbol('REDIS_CLIENT');

type RedisModuleOptions = Pick<RedisOptions, 'host' | 'port' | 'username' | 'password' | 'db'>;

@Global()
@Module({})
export class RedisModule {
  static forRootAsync(options: {
    useFactory?: (...args: any[]) => Promise<RedisModuleOptions> | RedisModuleOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: RedisModule,
      providers: [
        {
          provide: REDIS_CLIENT,
          useFactory: async (...args: any[]): Promise<Redis> => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            const redisOptions = await (options.useFactory?.(...args) || {});

            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
            const client: Redis = new Redis(redisOptions);

            await new Promise<void>((resolve, reject) => {
              client.once('ready', resolve);
              client.once('error', reject);
            });

            return client;
          },
          inject: options.inject,
        },
      ],
      exports: [REDIS_CLIENT],
    };
  }
}
